Context / variables (replace these before running)
project_name: <Project name, e.g. "Nainital Homestay">
target_audience: <Who is this for, e.g. "tourists looking for hill homestays">
main_goal: <Primary conversion, e.g. "book a stay / capture lead">
attachments: <List the files you uploaded to the workspace, e.g. "logo.png, hero.jpg, brochure.pdf, copy.docx">
page_builder: <"gutenberg" or "elementor">
wordpress_version: <e.g. "6.4">
preferred_fonts: <optional, e.g. "Inter for headings, Lora for body">
brand_colors: <optional hex codes, e.g. "#f5fcff, #ff914d, #68cef5">
contact_form_plugin: <"wpforms" or "contact-form-7">

Instruction to Replit AI (start here)
You are building a complete WordPress landing page for project_name using the attachments listed. First, analyze the attachments and extract: logo, hero image, all textual copy, testimonials, rates or features, and any PDFs that contain useful content. If any copy is missing, create concise, on-brand copy consistent with the attachment tone and target_audience.

Deliverables (produce each exactly as described):
1. A ready-to-install WordPress theme folder (zip-ready) named project_name-landing. Include:
   - style.css (theme header and clean styles)
   - index.php and page-landing.php (a specific page template for the landing)
   - functions.php with enqueues for styles and fonts and theme setup
   - screenshot.png (simple placeholder)
   - assets/ (logo, hero images, optimized images in multiple sizes)
   - templates/partials: header.php, footer.php, block-hero.php, block-features.php, block-testimonials.php, block-cta.php, block-footer-cta.php

2. If page_builder is "gutenberg": produce a fully commented block markup (HTML + WordPress block comments) for the landing page that can be pasted into the Gutenberg HTML editor to recreate the page. If page_builder is "elementor": produce a JSON export or clear step-by-step Elementor structure (sections, columns, widgets) with exact settings and copy to paste into Elementor.

3. Accessibility, SEO, and performance:
   - Include SEO meta title and description tailored to main_goal and target_audience.
   - Add Open Graph and Twitter Card tags.
   - Add JSON-LD Organization and WebPage schema for the landing page.
   - Ensure images have alt attributes and are responsive (srcset).
   - Add lazy-loading instructions and one small PHP snippet for server-side image sizes.
   - Include aria attributes for important interactive elements and a skip-to-content link.

4. Contact / conversion:
   - Provide a contact form block that integrates with chosen contact_form_plugin. Include field list (name, email, phone, check-in date if relevant), server-side validation notes, and spam mitigation (honeypot + nonce).
   - Provide CTA button code that opens a modal (simple JS) or links to the form anchor. Include analytics event hooks (example dataLayer push or GA4 event snippet).

5. Styling and copy:
   - Produce the final hero headline (one line), 1–2 supporting sub-headlines, 3 core feature blurbs, one social proof/testimonial, and a short closing CTA paragraph. Use language consistent with attachments and target_audience.
   - Create a minimal style.css with typography, layout grid, responsive breakpoints, and utility classes. Use preferred_fonts and brand_colors variables where given.

6. Plugins and install steps:
   - Recommend a short list of plugins with reasons and exact setup steps:
     - SEO (e.g. Rank Math or Yoast) — add meta
     - Contact form plugin chosen above
     - Image optimizer (e.g. ShortPixel or EWWW)
     - Caching plugin (one recommended)
     - Optional: Elementor (if selected)
   - Provide a clear step-by-step “Install and publish” README with exact file locations, how to upload the theme zip, and how to import the Gutenberg block or Elementor JSON.

7. Output format:
   - Show a file tree of the theme folder.
   - Provide the full content of style.css, page-landing.php (or block HTML), functions.php, and the critical partial templates (header.php, footer.php, block-hero.php).
   - Provide copy in a plain text section labeled PAGE_COPY so I can paste directly into Gutenberg/Elementor.
   - Provide a README.md with install steps and a short checklist for QA (mobile, accessibility, SEO, performance).

Constraints and quality notes:
- Keep design clean, mobile-first, and fast. No heavy frameworks.
- Use semantic HTML, clear class names, and inline comments explaining key parts.
- Use relative image paths (assets/) so I can zip and upload.
- Make sure the theme works with WordPress wordpress_version.
- If any attachment is ambiguous, infer tone from the largest or most obvious asset; explicitly note any assumptions in README.

Example hero (if attachments lack hero):
Hero headline: Book a calm hill homestay in Nainital
Subhead: Wake up to mountain views, local breakfast, and warm hospitality
Primary CTA: Check availability
Secondary CTA: View rooms

Now generate everything requested. Start by listing the attachments you found in the workspace and what you extracted from each. Then show the file tree, then paste the code files and PAGE_COPY, then README.md and plugin list.