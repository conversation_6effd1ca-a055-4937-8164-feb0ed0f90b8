import { Check<PERSON>ircle, Users, GraduationCap, Calendar } from "lucide-react";

export default function Hero() {
  const handleScheduleDemo = () => {
    const contactSection = document.getElementById('contact');
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  const handleLearnMore = () => {
    const aboutSection = document.getElementById('about');
    if (aboutSection) {
      aboutSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  return (
    <section className="hero-gradient py-20 lg:py-32 overflow-hidden">
      <div className="container mx-auto px-4 lg:px-6">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-8">
            <div className="space-y-6">
              <h1 className="text-4xl lg:text-6xl font-bold text-foreground leading-tight">
                Find Your <span className="text-primary">Dream Home</span> Today
              </h1>
              <p className="text-xl text-muted-foreground leading-relaxed">
                Discover the perfect home for your family with our curated selection of premium properties in the most desirable neighborhoods.
              </p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4">
              <button
                onClick={handleScheduleDemo}
                className="bg-primary text-primary-foreground px-8 py-4 rounded-lg text-lg font-semibold hover:bg-primary/90 transition-colors shadow-lg"
                data-testid="button-schedule-demo"
              >
                Schedule a Viewing
              </button>
              <button
                onClick={handleLearnMore}
                className="bg-secondary text-secondary-foreground px-8 py-4 rounded-lg text-lg font-semibold hover:bg-secondary/90 transition-colors"
                data-testid="button-learn-more"
              >
                Browse Properties
              </button>
            </div>
            
            <div className="flex items-center space-x-8 pt-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-foreground" data-testid="stat-students">1000+</div>
                <div className="text-sm text-muted-foreground">Happy Families</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-foreground" data-testid="stat-teachers">500+</div>
                <div className="text-sm text-muted-foreground">Properties Sold</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-foreground" data-testid="stat-years">15+</div>
                <div className="text-sm text-muted-foreground">Years Experience</div>
              </div>
            </div>
          </div>
          
          <div className="relative">
            <img
              src="/hero.jpg"
              alt="Beautiful modern home exterior with landscaping"
              className="rounded-2xl shadow-2xl w-full h-auto animate-float object-cover"
              loading="eager"
              data-testid="img-hero"
            />
            
            {/* Floating elements for visual interest */}
            <div className="absolute -top-6 -right-6 bg-accent text-accent-foreground p-4 rounded-full shadow-lg animate-float" style={{animationDelay: '-2s'}}>
              <CheckCircle className="w-8 h-8" />
            </div>
            
            <div className="absolute -bottom-6 -left-6 bg-secondary text-secondary-foreground p-4 rounded-full shadow-lg animate-float" style={{animationDelay: '-4s'}}>
              <GraduationCap className="w-8 h-8" />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
