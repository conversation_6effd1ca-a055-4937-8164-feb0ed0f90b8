/*
Theme Name: BrightMinds Academy Landing
Description: A modern, educational landing page theme for BrightMinds Academy - helping children 5-15 discover their potential through innovative learning programs.
Version: 1.0
Author: BrightMinds Academy
Tags: education, landing-page, responsive, accessibility
*/

:root {
  /* BrightMinds Academy Color Palette */
  --primary-light: #f5fcff;    /* Light blue background */
  --primary-orange: #ff914d;   /* Energetic orange for CTAs */
  --primary-green: #c9e265;    /* Fresh green for growth */
  --primary-blue: #68cef5;     /* Sky blue for trust */
  --primary-red: #ec104d;      /* Bold red for urgency */
  
  /* Semantic colors */
  --background: var(--primary-light);
  --foreground: #2d3748;
  --accent: var(--primary-orange);
  --secondary: var(--primary-blue);
  --success: var(--primary-green);
  --warning: var(--primary-red);
  
  /* Typography */
  --font-family-sans: 'Inter', system-ui, -apple-system, sans-serif;
  --font-family-heading: 'Poppins', var(--font-family-sans);
  
  /* Spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;
  --spacing-2xl: 4rem;
  
  /* Border radius */
  --border-radius: 0.5rem;
  --border-radius-lg: 1rem;
}

/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family-sans);
  font-size: 16px;
  line-height: 1.6;
  color: var(--foreground);
  background-color: var(--background);
  overflow-x: hidden;
}

img {
  max-width: 100%;
  height: auto;
  border-radius: var(--border-radius);
}

/* Skip to content link for accessibility */
.skip-link {
  position: absolute;
  left: -9999px;
  z-index: 999999;
  padding: var(--spacing-sm);
  background: var(--foreground);
  color: white;
  text-decoration: none;
  border-radius: var(--border-radius);
}

.skip-link:focus {
  left: var(--spacing-sm);
  top: var(--spacing-sm);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-heading);
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: var(--spacing-md);
}

h1 {
  font-size: clamp(2rem, 5vw, 3.5rem);
  color: var(--foreground);
}

h2 {
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  color: var(--foreground);
}

h3 {
  font-size: clamp(1.25rem, 3vw, 1.75rem);
}

p {
  margin-bottom: var(--spacing-md);
  font-size: 1.1rem;
}

/* Layout containers */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.section {
  padding: var(--spacing-2xl) 0;
}

.section-hero {
  padding: var(--spacing-xl) 0 var(--spacing-2xl);
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-blue) 100%);
}

.section-features {
  background: white;
}

.section-testimonials {
  background: var(--primary-light);
}

.section-cta {
  background: linear-gradient(135deg, var(--primary-orange) 0%, var(--primary-red) 100%);
  color: white;
}

/* Grid systems */
.grid {
  display: grid;
  gap: var(--spacing-lg);
}

.grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* Button styles */
.btn {
  display: inline-block;
  padding: var(--spacing-md) var(--spacing-xl);
  border: none;
  border-radius: var(--border-radius-lg);
  font-family: var(--font-family-heading);
  font-weight: 600;
  font-size: 1.1rem;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  line-height: 1;
}

.btn-primary {
  background: var(--primary-orange);
  color: white;
  box-shadow: 0 4px 15px rgba(255, 145, 77, 0.3);
}

.btn-primary:hover {
  background: #e8823f;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 145, 77, 0.4);
}

.btn-secondary {
  background: var(--primary-blue);
  color: white;
}

.btn-secondary:hover {
  background: #5ab8e8;
  transform: translateY(-2px);
}

.btn-outline {
  background: transparent;
  color: var(--primary-orange);
  border: 2px solid var(--primary-orange);
}

.btn-outline:hover {
  background: var(--primary-orange);
  color: white;
}

/* Header styles */
.header {
  background: white;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) 0;
}

.logo {
  font-family: var(--font-family-heading);
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-orange);
  text-decoration: none;
}

.nav {
  display: flex;
  list-style: none;
  gap: var(--spacing-lg);
}

.nav a {
  color: var(--foreground);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav a:hover {
  color: var(--primary-orange);
}

/* Hero section */
.hero {
  text-align: center;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero h1 {
  margin-bottom: var(--spacing-md);
  background: linear-gradient(135deg, var(--foreground) 0%, var(--primary-blue) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: #4a5568;
  margin-bottom: var(--spacing-xl);
}

.hero-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: var(--spacing-xl);
}

.hero-image {
  margin-top: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

/* Feature cards */
.feature-card {
  background: white;
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  text-align: center;
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto var(--spacing-md);
  background: var(--primary-green);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
}

.feature-card h3 {
  color: var(--primary-blue);
  margin-bottom: var(--spacing-md);
}

/* Testimonial */
.testimonial {
  background: white;
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.testimonial-quote {
  font-size: 1.25rem;
  font-style: italic;
  margin-bottom: var(--spacing-md);
  color: #4a5568;
}

.testimonial-author {
  font-weight: 600;
  color: var(--primary-blue);
}

/* Contact form */
.contact-form {
  background: white;
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  max-width: 600px;
  margin: 0 auto;
}

.form-group {
  margin-bottom: var(--spacing-md);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 600;
  color: var(--foreground);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--spacing-md);
  border: 2px solid #e2e8f0;
  border-radius: var(--border-radius);
  font-family: inherit;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.form-textarea {
  min-height: 120px;
  resize: vertical;
}

/* Footer */
.footer {
  background: var(--foreground);
  color: white;
  padding: var(--spacing-xl) 0;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.footer h4 {
  color: var(--primary-green);
  margin-bottom: var(--spacing-md);
}

.footer a {
  color: #cbd5e0;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer a:hover {
  color: var(--primary-orange);
}

/* Responsive design */
@media (max-width: 768px) {
  .hero-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .btn {
    width: 100%;
    max-width: 300px;
  }
  
  .nav {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .header-content {
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .section {
    padding: var(--spacing-lg) 0;
  }
}

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }

.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

.hidden { display: none; }
.sr-only { 
  position: absolute; 
  width: 1px; 
  height: 1px; 
  overflow: hidden; 
  clip: rect(0,0,0,0); 
}

/* Loading animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}